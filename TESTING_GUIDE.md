# User Management Testing Guide

This guide provides comprehensive testing steps for all user management features that have been fixed.

## Prerequisites

1. Ensure the application is running: `npm run dev`
2. Database is properly configured and accessible
3. Sonner toaster is properly configured in the layout

## 1. Account Creation Testing

### Test Case 1.1: Successful Account Creation
**Steps:**
1. Navigate to `/auth/signup`
2. Fill in all required fields:
   - Full name: "Test User"
   - Email: "<EMAIL>"
   - Password: "TestPassword123!"
   - Confirm Password: "TestPassword123!"
3. Click "Create account"

**Expected Results:**
- ✅ Success toast notification appears: "Account created successfully!"
- ✅ User is redirected to `/auth/signin`
- ✅ Account is created in the database
- ✅ No console errors

### Test Case 1.2: Account Creation with Existing Email
**Steps:**
1. Try to create account with same email as Test Case 1.1
2. Fill in form and submit

**Expected Results:**
- ❌ Error toast appears: "Account Creation Failed"
- ❌ Error message: "User with this email already exists"
- ❌ User remains on signup page

### Test Case 1.3: Account Creation Validation
**Steps:**
1. Try submitting form with invalid data:
   - Empty fields
   - Invalid email format
   - Password too short
   - Passwords don't match

**Expected Results:**
- ❌ Appropriate validation errors shown
- ❌ Form submission prevented
- ❌ Error toast notifications for validation failures

## 2. Login Testing

### Test Case 2.1: Successful Login
**Steps:**
1. Navigate to `/auth/signin`
2. Enter credentials from Test Case 1.1
3. Click "Sign in"

**Expected Results:**
- ✅ Success toast: "Welcome back!"
- ✅ User redirected to home page or callback URL
- ✅ User session established
- ✅ Navbar shows user profile/logout options

### Test Case 2.2: Failed Login
**Steps:**
1. Try login with incorrect credentials
2. Submit form

**Expected Results:**
- ❌ Error toast: "Authentication Failed"
- ❌ Error message about invalid credentials
- ❌ User remains on signin page

### Test Case 2.3: Google OAuth Login
**Steps:**
1. Click "Continue with Google" button
2. Complete Google OAuth flow

**Expected Results:**
- ✅ Successful authentication via Google
- ✅ User account created/linked if first time
- ✅ Success toast notification
- ✅ Redirect to home page

## 3. Profile Update Testing

### Test Case 3.1: Successful Profile Update
**Steps:**
1. Login and navigate to `/profile`
2. Click "Modifier" (Edit) button
3. Update profile information:
   - Name: "Updated Test User"
   - Phone: "+216 12 345 678"
   - Address: "123 Test Street"
   - City: "Tunis"
   - Postal Code: "1000"
4. Click "Sauvegarder" (Save)

**Expected Results:**
- ✅ Success toast: "Profil mis à jour"
- ✅ Form exits edit mode
- ✅ Updated information displayed
- ✅ Profile completion percentage updates
- ✅ Session updated with new name

### Test Case 3.2: Profile Update Validation
**Steps:**
1. Try to save profile with empty required fields (name)
2. Submit form

**Expected Results:**
- ❌ Validation prevents submission
- ❌ Error highlighting on required fields
- ❌ Form remains in edit mode

### Test Case 3.3: Profile Data Persistence
**Steps:**
1. Update profile (Test Case 3.1)
2. Refresh the page
3. Check if data persists

**Expected Results:**
- ✅ Updated data still displayed after refresh
- ✅ No data loss
- ✅ Profile completion percentage correct

## 4. Order History Testing

### Test Case 4.1: Place Order While Logged In
**Steps:**
1. Ensure user is logged in
2. Add items to cart
3. Go through checkout process
4. Complete order placement

**Expected Results:**
- ✅ Order successfully created
- ✅ Success toast notification
- ✅ Order saved to database with user association
- ✅ Order appears in user's order history

### Test Case 4.2: View Order History
**Steps:**
1. Navigate to `/profile`
2. Click "Commandes" tab
3. Verify orders are displayed

**Expected Results:**
- ✅ Orders list loads successfully
- ✅ Orders show correct information (number, date, total, status)
- ✅ Order count in profile statistics is accurate
- ✅ Filter tabs work correctly (All, Pending, Completed, Cancelled)

### Test Case 4.3: Order Details View
**Steps:**
1. In order history, click "Voir détails" on an order
2. Review order details modal

**Expected Results:**
- ✅ Order details modal opens
- ✅ All order information displayed correctly
- ✅ Order items list accurate
- ✅ Payment method and shipping info correct
- ✅ Order total calculations correct

### Test Case 4.4: Order History Refresh
**Steps:**
1. In order history, click "Actualiser" button
2. Wait for refresh to complete

**Expected Results:**
- ✅ Loading indicator appears
- ✅ Success toast: "Commandes actualisées"
- ✅ Order list refreshes
- ✅ Sync timestamp updates

## 5. Error Handling Testing

### Test Case 5.1: Network Errors
**Steps:**
1. Disconnect internet
2. Try various user management operations
3. Reconnect and retry

**Expected Results:**
- ❌ Appropriate error toasts for network failures
- ❌ User-friendly error messages
- ✅ Operations work after reconnection

### Test Case 5.2: Session Expiry
**Steps:**
1. Login and wait for session to expire
2. Try to update profile or access protected routes

**Expected Results:**
- ❌ Authentication error handling
- ❌ Redirect to login page
- ❌ Clear error messaging about session expiry

## 6. Toast Notification Consistency

### Test Case 6.1: Toast Appearance and Behavior
**Steps:**
1. Perform various operations that trigger toasts
2. Observe toast notifications

**Expected Results:**
- ✅ All toasts use Sonner (consistent styling)
- ✅ Success toasts are green-themed
- ✅ Error toasts are red-themed
- ✅ Toasts auto-dismiss after appropriate time
- ✅ Toasts are positioned consistently (top-right)
- ✅ Multiple toasts stack properly

## 7. Integration Testing

### Test Case 7.1: Complete User Journey
**Steps:**
1. Create new account
2. Login with new account
3. Update profile information
4. Place an order
5. View order in history
6. Logout and login again
7. Verify all data persists

**Expected Results:**
- ✅ Seamless user experience throughout
- ✅ All data persists correctly
- ✅ No broken functionality
- ✅ Consistent UI/UX

## Common Issues to Watch For

1. **Toast Conflicts**: Ensure no mix of Sonner and shadcn/ui toasts
2. **Session Management**: Verify session persistence across page refreshes
3. **Database Connections**: Check for database connection errors
4. **Form Validation**: Ensure client and server validation align
5. **Error Boundaries**: Verify graceful error handling
6. **Mobile Responsiveness**: Test on different screen sizes

## Performance Considerations

1. **Page Load Times**: Profile and order history should load quickly
2. **Database Queries**: Check for efficient queries in order fetching
3. **Image Loading**: Verify order item images load properly
4. **Memory Leaks**: Check for proper cleanup in React components

## Browser Compatibility

Test the above scenarios in:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Automated Testing Recommendations

For future development, consider adding:
1. Unit tests for API routes
2. Integration tests for user flows
3. E2E tests with Playwright or Cypress
4. Component testing with React Testing Library
