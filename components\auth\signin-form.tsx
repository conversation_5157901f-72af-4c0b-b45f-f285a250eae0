'use client';

import { useState, useEffect } from 'react';
import { signIn } from 'next-auth/react';
import {  useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { signInSchema, type SignInFormValues } from '@/lib/validations/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { EyeIcon, EyeOffIcon, LockIcon, MailIcon, ArrowRight } from 'lucide-react';
import { FcGoogle } from 'react-icons/fc';
import Link from 'next/link';
import { motion } from 'framer-motion';

export function SignInForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [authError, setAuthError] = useState<string>('');
  const [fieldErrors, setFieldErrors] = useState<{email?: string; password?: string}>({});
  const [successMessage, setSuccessMessage] = useState<string>('');
  // const router = useRouter();
  const searchParams = useSearchParams();

  const callbackUrl = searchParams.get('callbackUrl') || '/';

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<SignInFormValues>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Clear auth error when user starts typing
  const watchedFields = watch();
  useEffect(() => {
    if ((authError || Object.keys(fieldErrors).length > 0 || successMessage) && (watchedFields.email || watchedFields.password)) {
      setAuthError('');
      setFieldErrors({});
      setSuccessMessage('');
    }
  }, [watchedFields.email, watchedFields.password, authError, fieldErrors, successMessage]);

  const onSubmit = async (data: SignInFormValues) => {
    setIsLoading(true);
    setAuthError(''); // Clear previous errors
    setFieldErrors({}); // Clear field-specific errors
    setSuccessMessage(''); // Clear success message

    try {
      const result = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      console.log('SignIn result:', result); // Debug log

      if (result?.error) {
        // Authentication failed - set user-friendly field-specific errors
        console.log('Setting field errors'); // Debug log
        setFieldErrors({
          email: 'Email ou mot de passe incorrect',
          password: 'Veuillez vérifier vos informations et réessayer'
        });
      } else if (result?.ok) {
        // Success - user is authenticated
        setAuthError('');
        setFieldErrors({});
        setSuccessMessage('Connexion réussie ! Vous êtes maintenant connecté.');

        // Redirect after showing success message
        setTimeout(() => {
          window.location.href = callbackUrl;
        }, 2000);
      } else {
        // Unknown error
        setFieldErrors({
          email: 'Une erreur inattendue s\'est produite',
          password: 'Veuillez réessayer dans quelques instants'
        });
      }
    } catch (error) {
      console.error('Sign in error:', error);
      setFieldErrors({
        email: 'Problème de connexion au serveur',
        password: 'Veuillez vérifier votre connexion internet'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    try {
      await signIn('google', { callbackUrl });
    } catch (error) {
      console.error('Google sign in error:', error);
      toast.error('Error', {
        description: 'Failed to sign in with Google',
      });
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Compact Header */}
      <div className="text-center lg:text-left space-y-2">
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-2xl font-bold text-gray-900"
        >
          Sign in
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-gray-600 text-sm"
        >
          Welcome back! Please enter your details.
        </motion.p>
      </div>

      {/* Google Sign In */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Button
          type="button"
          variant="outline"
          onClick={handleGoogleSignIn}
          disabled={isGoogleLoading || isLoading}
          className="w-full h-11 text-sm border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-200"
        >
          <FcGoogle className="mr-2 h-4 w-4" />
          {isGoogleLoading ? 'Signing in...' : 'Continue with Google'}
        </Button>
      </motion.div>

      {/* Divider */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="relative"
      >
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-200" />
        </div>
        <div className="relative flex justify-center text-xs">
          <span className="bg-white px-3 text-gray-500">Or continue with email</span>
        </div>
      </motion.div>

      {/* Credentials Form */}
      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        onSubmit={handleSubmit(onSubmit)}
        className="space-y-4"
      >
        <div className="space-y-3">
          <div className="space-y-1">
            <Label htmlFor="email" className="text-sm font-medium text-gray-700">
              Email
            </Label>
            <div className="relative">
              <MailIcon className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 ${
                errors.email || fieldErrors.email ? 'text-red-400' : 'text-gray-400'
              }`} />
              <Input
                id="email"
                type="email"
                placeholder="Entrez votre email"
                className={`pl-9 h-10 border transition-colors ${
                  errors.email || fieldErrors.email
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                    : 'border-gray-200 focus:border-green-500 focus:ring-green-500'
                }`}
                {...register('email')}
                disabled={isLoading || isGoogleLoading}
              />
            </div>
            {(errors.email || fieldErrors.email) && (
              <p className="text-sm text-red-600 flex items-center gap-1 font-medium">
                <span className="w-2 h-2 bg-red-600 rounded-full"></span>
                {errors.email?.message || fieldErrors.email}
              </p>
            )}
          </div>

          <div className="space-y-1">
            <Label htmlFor="password" className="text-sm font-medium text-gray-700">
              Password
            </Label>
            <div className="relative">
              <LockIcon className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 ${
                errors.password || fieldErrors.password ? 'text-red-400' : 'text-gray-400'
              }`} />
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Entrez votre mot de passe"
                className={`pl-9 pr-10 h-10 border transition-colors ${
                  errors.password || fieldErrors.password
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                    : 'border-gray-200 focus:border-green-500 focus:ring-green-500'
                }`}
                {...register('password')}
                disabled={isLoading || isGoogleLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors ${
                  errors.password || fieldErrors.password
                    ? 'text-red-400 hover:text-red-600'
                    : 'text-gray-400 hover:text-gray-600'
                }`}
                disabled={isLoading || isGoogleLoading}
              >
                {showPassword ? (
                  <EyeOffIcon className="h-4 w-4" />
                ) : (
                  <EyeIcon className="h-4 w-4" />
                )}
              </button>
            </div>
            {(errors.password || fieldErrors.password) && (
              <p className="text-sm text-red-600 flex items-center gap-1 font-medium">
                <span className="w-2 h-2 bg-red-600 rounded-full"></span>
                {errors.password?.message || fieldErrors.password}
              </p>
            )}
          </div>
        </div>



        {/* Success Message */}
        {successMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start gap-3"
          >
            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs text-white font-bold">✓</span>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-green-800 mb-1">
                Connexion réussie
              </p>
              <p className="text-sm text-green-700 leading-relaxed">
                {successMessage}
              </p>
            </div>
          </motion.div>
        )}

        <Button
          type="submit"
          disabled={isLoading || isGoogleLoading}
          className="w-full h-10 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Signing in...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              Sign in
              <ArrowRight className="w-3 h-3" />
            </div>
          )}
        </Button>
      </motion.form>

      {/* Sign up link */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="text-center"
      >
        <p className="text-xs text-gray-600">
          Don't have an account?{' '}
          <Link
            href="/auth/signup"
            className="font-medium text-green-600 hover:text-green-500 transition-colors"
          >
            Sign up for free
          </Link>
        </p>
      </motion.div>
    </div>
  );
}
