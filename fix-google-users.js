// Script to fix Google OAuth users and ensure proper database records
const { PrismaClient } = require('@prisma/client');

async function fixGoogleUsers() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔄 Checking for Google OAuth users...');
    
    // Get all users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        createdAt: true,
      }
    });
    
    console.log(`📊 Found ${users.length} users in database`);
    
    // List all users for debugging
    users.forEach(user => {
      console.log(`👤 User: ${user.email} | ID: ${user.id} | Name: ${user.name} | Has Image: ${!!user.image}`);
    });
    
    // Check for users with Google images (contains googleapis.com)
    const googleUsers = users.filter(user => 
      user.image && user.image.includes('googleapis.com')
    );
    
    console.log(`🔍 Found ${googleUsers.length} users with Google profile images`);
    
    if (googleUsers.length > 0) {
      console.log('✅ Google OAuth users are properly stored in database');
      googleUsers.forEach(user => {
        console.log(`  📸 ${user.email}: ${user.image}`);
      });
    } else {
      console.log('⚠️  No users with Google profile images found');
    }
    
    // Check for any orphaned sessions (this would require checking NextAuth tables)
    console.log('\n🔍 Database structure check complete');
    
  } catch (error) {
    console.error('❌ Error checking Google users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixGoogleUsers();
